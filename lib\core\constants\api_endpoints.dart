/// API endpoints for Al Ameen Sales App
class ApiEndpoints {
  // Base URLs
  static const String baseUrl = 'https://api.alameen.com/v1';
  static const String devBaseUrl = 'https://dev-api.alameen.com/v1';
  static const String stagingBaseUrl = 'https://staging-api.alameen.com/v1';

  // Authentication Endpoints
  static const String login = '/auth/login';
  static const String logout = '/auth/logout';
  static const String refreshToken = '/auth/refresh';
  static const String forgotPassword = '/auth/forgot-password';
  static const String resetPassword = '/auth/reset-password';
  static const String changePassword = '/auth/change-password';

  // User Endpoints
  static const String profile = '/user/profile';
  static const String updateProfile = '/user/profile';
  static const String uploadAvatar = '/user/avatar';

  // Order Endpoints
  static const String orders = '/orders';
  static const String createOrder = '/orders';
  static const String orderDetails = '/orders/{id}';
  static const String updateOrder = '/orders/{id}';
  static const String deleteOrder = '/orders/{id}';
  static const String ordersByCustomer = '/orders/customer/{customerId}';
  static const String orderStatuses = '/orders/statuses';
  static const String orderReport = '/orders/report';

  // Customer Endpoints
  static const String customers = '/customers';
  static const String createCustomer = '/customers';
  static const String customerDetails = '/customers/{id}';
  static const String updateCustomer = '/customers/{id}';
  static const String deleteCustomer = '/customers/{id}';
  static const String customerSearch = '/customers/search';
  static const String customerOrderHistory = '/customers/{id}/orders';

  // Visit Endpoints
  static const String visits = '/visits';
  static const String createVisit = '/visits';
  static const String visitDetails = '/visits/{id}';
  static const String updateVisit = '/visits/{id}';
  static const String deleteVisit = '/visits/{id}';
  static const String visitsByCustomer = '/visits/customer/{customerId}';
  static const String visitsByDate = '/visits/date/{date}';
  static const String completeVisit = '/visits/{id}/complete';

  // Invoice Endpoints
  static const String invoices = '/invoices';
  static const String createInvoice = '/invoices';
  static const String invoiceDetails = '/invoices/{id}';
  static const String updateInvoice = '/invoices/{id}';
  static const String deleteInvoice = '/invoices/{id}';
  static const String invoicesByCustomer = '/invoices/customer/{customerId}';
  static const String invoicePdf = '/invoices/{id}/pdf';
  static const String sendInvoice = '/invoices/{id}/send';

  // Return Endpoints
  static const String returns = '/returns';
  static const String createReturn = '/returns';
  static const String returnDetails = '/returns/{id}';
  static const String updateReturn = '/returns/{id}';
  static const String deleteReturn = '/returns/{id}';
  static const String returnsByCustomer = '/returns/customer/{customerId}';
  static const String returnsByOrder = '/returns/order/{orderId}';
  static const String approveReturn = '/returns/{id}/approve';
  static const String rejectReturn = '/returns/{id}/reject';

  // Notification Endpoints
  static const String notifications = '/notifications';
  static const String notificationDetails = '/notifications/{id}';
  static const String markAsRead = '/notifications/{id}/read';
  static const String markAllAsRead = '/notifications/read-all';
  static const String unreadCount = '/notifications/unread-count';

  // Product Endpoints
  static const String products = '/products';
  static const String productDetails = '/products/{id}';
  static const String productSearch = '/products/search';
  static const String productCategories = '/products/categories';

  // Dashboard Endpoints
  static const String dashboardStats = '/dashboard/stats';
  static const String salesSummary = '/dashboard/sales-summary';
  static const String recentActivities = '/dashboard/recent-activities';

  // Report Endpoints
  static const String salesReport = '/reports/sales';
  static const String customerReport = '/reports/customers';
  static const String visitReport = '/reports/visits';
  static const String invoiceReport = '/reports/invoices';
  static const String returnReport = '/reports/returns';

  // File Upload Endpoints
  static const String uploadFile = '/files/upload';
  static const String downloadFile = '/files/{id}/download';

  // Utility Methods
  static String getOrderDetails(String orderId) {
    return orderDetails.replaceAll('{id}', orderId);
  }

  static String getCustomerDetails(String customerId) {
    return customerDetails.replaceAll('{id}', customerId);
  }

  static String getVisitDetails(String visitId) {
    return visitDetails.replaceAll('{id}', visitId);
  }

  static String getInvoiceDetails(String invoiceId) {
    return invoiceDetails.replaceAll('{id}', invoiceId);
  }

  static String getReturnDetails(String returnId) {
    return returnDetails.replaceAll('{id}', returnId);
  }

  static String getOrdersByCustomer(String customerId) {
    return ordersByCustomer.replaceAll('{customerId}', customerId);
  }

  static String getVisitsByCustomer(String customerId) {
    return visitsByCustomer.replaceAll('{customerId}', customerId);
  }

  static String getInvoicesByCustomer(String customerId) {
    return invoicesByCustomer.replaceAll('{customerId}', customerId);
  }

  static String getReturnsByCustomer(String customerId) {
    return returnsByCustomer.replaceAll('{customerId}', customerId);
  }

  static String getReturnsByOrder(String orderId) {
    return returnsByOrder.replaceAll('{orderId}', orderId);
  }

  static String getVisitsByDate(String date) {
    return visitsByDate.replaceAll('{date}', date);
  }

  static String getInvoicePdf(String invoiceId) {
    return invoicePdf.replaceAll('{id}', invoiceId);
  }

  static String getDownloadFile(String fileId) {
    return downloadFile.replaceAll('{id}', fileId);
  }
}
