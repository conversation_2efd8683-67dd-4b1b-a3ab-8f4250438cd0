/// Application strings for Al Ameen Sales App
class AppStrings {
  // App Information
  static const String appName = 'Al Ameen Sales';
  static const String appTagline = 'Your Sales Companion';

  // Authentication
  static const String login = 'Login';
  static const String logout = 'Logout';
  static const String email = 'Email';
  static const String password = 'Password';
  static const String forgotPassword = 'Forgot Password?';
  static const String rememberMe = 'Remember Me';
  static const String loginButton = 'Sign In';
  static const String loggingIn = 'Signing In...';
  static const String welcome = 'Welcome';
  static const String welcomeBack = 'Welcome Back';

  // Navigation
  static const String dashboard = 'Dashboard';
  static const String orders = 'Orders';
  static const String customers = 'Customers';
  static const String visits = 'Visits';
  static const String invoices = 'Invoices';
  static const String returns = 'Returns';
  static const String notifications = 'Notifications';
  static const String profile = 'Profile';
  static const String settings = 'Settings';

  // Common Actions
  static const String add = 'Add';
  static const String edit = 'Edit';
  static const String delete = 'Delete';
  static const String save = 'Save';
  static const String cancel = 'Cancel';
  static const String confirm = 'Confirm';
  static const String ok = 'OK';
  static const String yes = 'Yes';
  static const String no = 'No';
  static const String search = 'Search';
  static const String filter = 'Filter';
  static const String sort = 'Sort';
  static const String refresh = 'Refresh';
  static const String loading = 'Loading...';
  static const String retry = 'Retry';
  static const String viewDetails = 'View Details';
  static const String viewAll = 'View All';

  // Orders
  static const String newOrder = 'New Order';
  static const String createOrder = 'Create Order';
  static const String orderDetails = 'Order Details';
  static const String orderNumber = 'Order Number';
  static const String orderDate = 'Order Date';
  static const String orderStatus = 'Order Status';
  static const String orderTotal = 'Order Total';
  static const String orderItems = 'Order Items';
  static const String addProduct = 'Add Product';
  static const String quantity = 'Quantity';
  static const String price = 'Price';
  static const String total = 'Total';
  static const String subtotal = 'Subtotal';
  static const String tax = 'Tax';
  static const String discount = 'Discount';

  // Customers
  static const String newCustomer = 'New Customer';
  static const String addCustomer = 'Add Customer';
  static const String customerDetails = 'Customer Details';
  static const String customerName = 'Customer Name';
  static const String customerCode = 'Customer Code';
  static const String customerPhone = 'Phone Number';
  static const String customerEmail = 'Email Address';
  static const String customerAddress = 'Address';
  static const String customerCity = 'City';
  static const String customerType = 'Customer Type';
  static const String contactPerson = 'Contact Person';

  // Visits
  static const String newVisit = 'New Visit';
  static const String scheduleVisit = 'Schedule Visit';
  static const String visitDetails = 'Visit Details';
  static const String visitDate = 'Visit Date';
  static const String visitTime = 'Visit Time';
  static const String visitPurpose = 'Visit Purpose';
  static const String visitNotes = 'Visit Notes';
  static const String visitStatus = 'Visit Status';
  static const String completeVisit = 'Complete Visit';
  static const String visitDuration = 'Duration';

  // Invoices
  static const String newInvoice = 'New Invoice';
  static const String createInvoice = 'Create Invoice';
  static const String invoiceDetails = 'Invoice Details';
  static const String invoiceNumber = 'Invoice Number';
  static const String invoiceDate = 'Invoice Date';
  static const String dueDate = 'Due Date';
  static const String invoiceStatus = 'Invoice Status';
  static const String invoiceAmount = 'Invoice Amount';
  static const String generatePdf = 'Generate PDF';
  static const String sendInvoice = 'Send Invoice';
  static const String printInvoice = 'Print Invoice';

  // Returns
  static const String newReturn = 'New Return';
  static const String createReturn = 'Create Return';
  static const String returnDetails = 'Return Details';
  static const String returnNumber = 'Return Number';
  static const String returnDate = 'Return Date';
  static const String returnReason = 'Return Reason';
  static const String returnStatus = 'Return Status';
  static const String returnAmount = 'Return Amount';
  static const String returnItems = 'Return Items';

  // Notifications
  static const String markAsRead = 'Mark as Read';
  static const String markAllAsRead = 'Mark All as Read';
  static const String unreadNotifications = 'Unread Notifications';
  static const String noNotifications = 'No Notifications';

  // Status Labels
  static const String pending = 'Pending';
  static const String confirmed = 'Confirmed';
  static const String processing = 'Processing';
  static const String shipped = 'Shipped';
  static const String delivered = 'Delivered';
  static const String cancelled = 'Cancelled';
  static const String scheduled = 'Scheduled';
  static const String inProgress = 'In Progress';
  static const String completed = 'Completed';
  static const String draft = 'Draft';
  static const String sent = 'Sent';
  static const String paid = 'Paid';
  static const String overdue = 'Overdue';
  static const String requested = 'Requested';
  static const String approved = 'Approved';
  static const String rejected = 'Rejected';

  // Form Labels
  static const String name = 'Name';
  static const String description = 'Description';
  static const String date = 'Date';
  static const String time = 'Time';
  static const String amount = 'Amount';
  static const String notes = 'Notes';
  static const String comments = 'Comments';
  static const String reason = 'Reason';
  static const String type = 'Type';
  static const String category = 'Category';
  static const String status = 'Status';

  // Validation Messages
  static const String fieldRequired = 'This field is required';
  static const String invalidEmail = 'Please enter a valid email address';
  static const String invalidPhone = 'Please enter a valid phone number';
  static const String passwordTooShort = 'Password must be at least 6 characters';
  static const String passwordsDoNotMatch = 'Passwords do not match';
  static const String invalidAmount = 'Please enter a valid amount';
  static const String invalidQuantity = 'Please enter a valid quantity';

  // Error Messages
  static const String errorOccurred = 'An error occurred';
  static const String networkError = 'Network connection error';
  static const String serverError = 'Server error';
  static const String unauthorizedError = 'Unauthorized access';
  static const String notFoundError = 'Resource not found';
  static const String validationError = 'Validation error';
  static const String unknownError = 'Unknown error occurred';

  // Success Messages
  static const String loginSuccess = 'Login successful';
  static const String logoutSuccess = 'Logout successful';
  static const String saveSuccess = 'Saved successfully';
  static const String updateSuccess = 'Updated successfully';
  static const String deleteSuccess = 'Deleted successfully';
  static const String createSuccess = 'Created successfully';

  // Empty States
  static const String noData = 'No data available';
  static const String noOrders = 'No orders found';
  static const String noCustomers = 'No customers found';
  static const String noVisits = 'No visits found';
  static const String noInvoices = 'No invoices found';
  static const String noReturns = 'No returns found';
  static const String noResults = 'No results found';

  // Date & Time
  static const String today = 'Today';
  static const String yesterday = 'Yesterday';
  static const String tomorrow = 'Tomorrow';
  static const String thisWeek = 'This Week';
  static const String thisMonth = 'This Month';
  static const String selectDate = 'Select Date';
  static const String selectTime = 'Select Time';

  // File Operations
  static const String upload = 'Upload';
  static const String download = 'Download';
  static const String share = 'Share';
  static const String export = 'Export';
  static const String import = 'Import';

  // Confirmation Messages
  static const String confirmDelete = 'Are you sure you want to delete this item?';
  static const String confirmLogout = 'Are you sure you want to logout?';
  static const String confirmCancel = 'Are you sure you want to cancel? Unsaved changes will be lost.';

  // Roles
  static const String retailSalesRep = 'Retail Sales Representative';
  static const String wholesaleSalesRep = 'Wholesale Sales Representative';
}
