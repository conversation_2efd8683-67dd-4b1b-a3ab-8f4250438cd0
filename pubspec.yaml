name: al_ameen_app
description: "Al Ameen Sales App - Dual-role mobile application for Retail and Wholesale Sales Representatives"
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: ^3.7.2
  flutter: ">=3.0.0"

dependencies:
  flutter:
    sdk: flutter

  # State Management & Navigation
  get: ^4.6.6

  # HTTP Client
  http: ^1.1.0

  # Responsive Design
  flutter_screenutil: ^5.9.0

  # Typography
  google_fonts: ^6.1.0

  # Vector Graphics
  flutter_svg: ^2.0.7

  # Secure Storage
  flutter_secure_storage: ^9.0.0

  # Animations
  flutter_animate: ^4.2.0+1

  # Date & Time
  intl: ^0.19.0

  # JSON Serialization
  json_annotation: ^4.8.1

  # UI Components
  cupertino_icons: ^1.0.8

dev_dependencies:
  flutter_test:
    sdk: flutter

  # Code Generation
  build_runner: ^2.4.7
  json_serializable: ^6.7.1

  # Linting
  flutter_lints: ^5.0.0

flutter:
  uses-material-design: true

  # Assets
  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/

  # Fonts
  fonts:
    - family: AlAmeenIcons
      fonts:
        - asset: assets/fonts/al_ameen_icons.ttf
