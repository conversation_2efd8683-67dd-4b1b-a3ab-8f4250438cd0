import 'package:flutter/material.dart';

/// Application color scheme for Al Ameen Sales App
class AppColors {
  // Primary Colors
  static const Color primary = Color(0xFF1976D2); // Blue
  static const Color primaryLight = Color(0xFF42A5F5);
  static const Color primaryDark = Color(0xFF0D47A1);
  static const Color primaryContainer = Color(0xFFE3F2FD);

  // Secondary Colors
  static const Color secondary = Color(0xFF388E3C); // Green
  static const Color secondaryLight = Color(0xFF66BB6A);
  static const Color secondaryDark = Color(0xFF1B5E20);
  static const Color secondaryContainer = Color(0xFFE8F5E8);

  // Accent Colors
  static const Color accent = Color(0xFFFF9800); // Orange
  static const Color accentLight = Color(0xFFFFB74D);
  static const Color accentDark = Color(0xFFE65100);

  // Background Colors
  static const Color background = Color(0xFFFAFAFA);
  static const Color surface = Color(0xFFFFFFFF);
  static const Color surfaceVariant = Color(0xFFF5F5F5);
  static const Color surfaceContainer = Color(0xFFEEEEEE);

  // Text Colors
  static const Color onPrimary = Color(0xFFFFFFFF);
  static const Color onSecondary = Color(0xFFFFFFFF);
  static const Color onBackground = Color(0xFF212121);
  static const Color onSurface = Color(0xFF212121);
  static const Color onSurfaceVariant = Color(0xFF757575);

  // Status Colors
  static const Color success = Color(0xFF4CAF50);
  static const Color successLight = Color(0xFF81C784);
  static const Color successDark = Color(0xFF2E7D32);

  static const Color warning = Color(0xFFFF9800);
  static const Color warningLight = Color(0xFFFFB74D);
  static const Color warningDark = Color(0xFFE65100);

  static const Color error = Color(0xFFF44336);
  static const Color errorLight = Color(0xFFE57373);
  static const Color errorDark = Color(0xFFD32F2F);

  static const Color info = Color(0xFF2196F3);
  static const Color infoLight = Color(0xFF64B5F6);
  static const Color infoDark = Color(0xFF1976D2);

  // Neutral Colors
  static const Color grey50 = Color(0xFFFAFAFA);
  static const Color grey100 = Color(0xFFF5F5F5);
  static const Color grey200 = Color(0xFFEEEEEE);
  static const Color grey300 = Color(0xFFE0E0E0);
  static const Color grey400 = Color(0xFFBDBDBD);
  static const Color grey500 = Color(0xFF9E9E9E);
  static const Color grey600 = Color(0xFF757575);
  static const Color grey700 = Color(0xFF616161);
  static const Color grey800 = Color(0xFF424242);
  static const Color grey900 = Color(0xFF212121);

  // Order Status Colors
  static const Color orderPending = Color(0xFFFF9800);
  static const Color orderConfirmed = Color(0xFF2196F3);
  static const Color orderProcessing = Color(0xFF9C27B0);
  static const Color orderShipped = Color(0xFF3F51B5);
  static const Color orderDelivered = Color(0xFF4CAF50);
  static const Color orderCancelled = Color(0xFFF44336);

  // Visit Status Colors
  static const Color visitScheduled = Color(0xFF2196F3);
  static const Color visitInProgress = Color(0xFFFF9800);
  static const Color visitCompleted = Color(0xFF4CAF50);
  static const Color visitCancelled = Color(0xFFF44336);

  // Invoice Status Colors
  static const Color invoiceDraft = Color(0xFF9E9E9E);
  static const Color invoiceSent = Color(0xFF2196F3);
  static const Color invoicePaid = Color(0xFF4CAF50);
  static const Color invoiceOverdue = Color(0xFFF44336);

  // Return Status Colors
  static const Color returnRequested = Color(0xFFFF9800);
  static const Color returnApproved = Color(0xFF4CAF50);
  static const Color returnRejected = Color(0xFFF44336);
  static const Color returnProcessing = Color(0xFF9C27B0);
  static const Color returnCompleted = Color(0xFF4CAF50);

  // Gradient Colors
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [primary, primaryLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient secondaryGradient = LinearGradient(
    colors: [secondary, secondaryLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient accentGradient = LinearGradient(
    colors: [accent, accentLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  // Shadow Colors
  static const Color shadowLight = Color(0x1A000000);
  static const Color shadowMedium = Color(0x33000000);
  static const Color shadowDark = Color(0x4D000000);

  // Border Colors
  static const Color borderLight = Color(0xFFE0E0E0);
  static const Color borderMedium = Color(0xFFBDBDBD);
  static const Color borderDark = Color(0xFF757575);

  // Overlay Colors
  static const Color overlayLight = Color(0x0A000000);
  static const Color overlayMedium = Color(0x1F000000);
  static const Color overlayDark = Color(0x33000000);

  // Utility Methods
  static Color getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return orderPending;
      case 'confirmed':
        return orderConfirmed;
      case 'processing':
        return orderProcessing;
      case 'shipped':
        return orderShipped;
      case 'delivered':
        return orderDelivered;
      case 'cancelled':
        return orderCancelled;
      case 'scheduled':
        return visitScheduled;
      case 'in_progress':
        return visitInProgress;
      case 'completed':
        return visitCompleted;
      case 'draft':
        return invoiceDraft;
      case 'sent':
        return invoiceSent;
      case 'paid':
        return invoicePaid;
      case 'overdue':
        return invoiceOverdue;
      case 'requested':
        return returnRequested;
      case 'approved':
        return returnApproved;
      case 'rejected':
        return returnRejected;
      default:
        return grey500;
    }
  }

  static Color getContrastColor(Color color) {
    // Calculate luminance
    final luminance = color.computeLuminance();
    return luminance > 0.5 ? Colors.black : Colors.white;
  }

  static Color withOpacity(Color color, double opacity) {
    return color.withOpacity(opacity);
  }
}
