/// Application-wide constants for Al Ameen Sales App
class AppConstants {
  // App Information
  static const String appName = 'Al Ameen Sales';
  static const String appVersion = '1.0.0';
  static const String packageName = 'com.alameen.sales_app';

  // User Roles
  static const String retailRole = 'retail';
  static const String wholesaleRole = 'wholesale';

  // Storage Keys
  static const String tokenKey = 'auth_token';
  static const String refreshTokenKey = 'refresh_token';
  static const String userKey = 'user_data';
  static const String userRoleKey = 'user_role';
  static const String isFirstLaunchKey = 'is_first_launch';

  // API Configuration
  static const int connectionTimeout = 30000; // 30 seconds
  static const int receiveTimeout = 30000; // 30 seconds
  static const int sendTimeout = 30000; // 30 seconds

  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;

  // Animation Durations
  static const Duration splashDuration = Duration(seconds: 3);
  static const Duration animationDuration = Duration(milliseconds: 300);
  static const Duration longAnimationDuration = Duration(milliseconds: 500);

  // UI Constants
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double borderRadius = 12.0;
  static const double smallBorderRadius = 8.0;
  static const double largeBorderRadius = 16.0;

  // Order Status
  static const String orderStatusPending = 'pending';
  static const String orderStatusConfirmed = 'confirmed';
  static const String orderStatusProcessing = 'processing';
  static const String orderStatusShipped = 'shipped';
  static const String orderStatusDelivered = 'delivered';
  static const String orderStatusCancelled = 'cancelled';

  // Visit Status
  static const String visitStatusScheduled = 'scheduled';
  static const String visitStatusInProgress = 'in_progress';
  static const String visitStatusCompleted = 'completed';
  static const String visitStatusCancelled = 'cancelled';

  // Invoice Status
  static const String invoiceStatusDraft = 'draft';
  static const String invoiceStatusSent = 'sent';
  static const String invoiceStatusPaid = 'paid';
  static const String invoiceStatusOverdue = 'overdue';
  static const String invoiceStatusCancelled = 'cancelled';

  // Return Status
  static const String returnStatusRequested = 'requested';
  static const String returnStatusApproved = 'approved';
  static const String returnStatusRejected = 'rejected';
  static const String returnStatusProcessing = 'processing';
  static const String returnStatusCompleted = 'completed';

  // Notification Types
  static const String notificationTypeOrder = 'order';
  static const String notificationTypeVisit = 'visit';
  static const String notificationTypeInvoice = 'invoice';
  static const String notificationTypeReturn = 'return';
  static const String notificationTypeGeneral = 'general';

  // Date Formats
  static const String dateFormat = 'dd/MM/yyyy';
  static const String timeFormat = 'HH:mm';
  static const String dateTimeFormat = 'dd/MM/yyyy HH:mm';
  static const String apiDateFormat = 'yyyy-MM-dd';
  static const String apiDateTimeFormat = 'yyyy-MM-ddTHH:mm:ss.SSSZ';

  // Validation
  static const int minPasswordLength = 6;
  static const int maxPasswordLength = 50;
  static const int minNameLength = 2;
  static const int maxNameLength = 100;
  static const int maxDescriptionLength = 500;

  // File Upload
  static const int maxFileSize = 5 * 1024 * 1024; // 5MB
  static const List<String> allowedImageTypes = ['jpg', 'jpeg', 'png'];
  static const List<String> allowedDocumentTypes = ['pdf', 'doc', 'docx'];

  // Error Messages
  static const String networkErrorMessage = 'Network connection error. Please check your internet connection.';
  static const String serverErrorMessage = 'Server error occurred. Please try again later.';
  static const String unauthorizedErrorMessage = 'Session expired. Please login again.';
  static const String validationErrorMessage = 'Please check your input and try again.';
  static const String unknownErrorMessage = 'An unexpected error occurred. Please try again.';

  // Success Messages
  static const String loginSuccessMessage = 'Login successful';
  static const String logoutSuccessMessage = 'Logout successful';
  static const String saveSuccessMessage = 'Data saved successfully';
  static const String updateSuccessMessage = 'Data updated successfully';
  static const String deleteSuccessMessage = 'Data deleted successfully';

  // Empty State Messages
  static const String noOrdersMessage = 'No orders found';
  static const String noCustomersMessage = 'No customers found';
  static const String noVisitsMessage = 'No visits found';
  static const String noInvoicesMessage = 'No invoices found';
  static const String noReturnsMessage = 'No returns found';
  static const String noNotificationsMessage = 'No notifications found';

  // Search
  static const String searchHint = 'Search...';
  static const int searchDebounceMs = 500;
  static const int minSearchLength = 2;
}
