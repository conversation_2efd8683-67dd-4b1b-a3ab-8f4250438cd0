import 'package:intl/intl.dart';
import '../constants/app_constants.dart';

/// Date and time utilities for Al Ameen Sales App
class AppDateUtils {
  // Prevent instantiation
  AppDateUtils._();

  // Date formatters
  static final DateFormat _dateFormatter = DateFormat(AppConstants.dateFormat);
  static final DateFormat _timeFormatter = DateFormat(AppConstants.timeFormat);
  static final DateFormat _dateTimeFormatter = DateFormat(AppConstants.dateTimeFormat);
  static final DateFormat _apiDateFormatter = DateFormat(AppConstants.apiDateFormat);
  static final DateFormat _apiDateTimeFormatter = DateFormat(AppConstants.apiDateTimeFormat);

  /// Format date to display format (dd/MM/yyyy)
  static String formatDate(DateTime date) {
    return _dateFormatter.format(date);
  }

  /// Format time to display format (HH:mm)
  static String formatTime(DateTime time) {
    return _timeFormatter.format(time);
  }

  /// Format date and time to display format (dd/MM/yyyy HH:mm)
  static String formatDateTime(DateTime dateTime) {
    return _dateTimeFormatter.format(dateTime);
  }

  /// Format date for API (yyyy-MM-dd)
  static String formatDateForApi(DateTime date) {
    return _apiDateFormatter.format(date);
  }

  /// Format date and time for API (yyyy-MM-ddTHH:mm:ss.SSSZ)
  static String formatDateTimeForApi(DateTime dateTime) {
    return _apiDateTimeFormatter.format(dateTime);
  }

  /// Parse date from string
  static DateTime? parseDate(String dateString) {
    try {
      return _dateFormatter.parse(dateString);
    } catch (e) {
      return null;
    }
  }

  /// Parse time from string
  static DateTime? parseTime(String timeString) {
    try {
      return _timeFormatter.parse(timeString);
    } catch (e) {
      return null;
    }
  }

  /// Parse date and time from string
  static DateTime? parseDateTime(String dateTimeString) {
    try {
      return _dateTimeFormatter.parse(dateTimeString);
    } catch (e) {
      return null;
    }
  }

  /// Parse date from API format
  static DateTime? parseDateFromApi(String dateString) {
    try {
      return _apiDateFormatter.parse(dateString);
    } catch (e) {
      return null;
    }
  }

  /// Parse date and time from API format
  static DateTime? parseDateTimeFromApi(String dateTimeString) {
    try {
      return DateTime.parse(dateTimeString);
    } catch (e) {
      return null;
    }
  }

  /// Get current date
  static DateTime get now => DateTime.now();

  /// Get current date without time
  static DateTime get today {
    final now = DateTime.now();
    return DateTime(now.year, now.month, now.day);
  }

  /// Get yesterday's date
  static DateTime get yesterday {
    final now = DateTime.now();
    return DateTime(now.year, now.month, now.day - 1);
  }

  /// Get tomorrow's date
  static DateTime get tomorrow {
    final now = DateTime.now();
    return DateTime(now.year, now.month, now.day + 1);
  }

  /// Get start of week (Monday)
  static DateTime get startOfWeek {
    final now = DateTime.now();
    final daysFromMonday = now.weekday - 1;
    return DateTime(now.year, now.month, now.day - daysFromMonday);
  }

  /// Get end of week (Sunday)
  static DateTime get endOfWeek {
    final startOfWeek = AppDateUtils.startOfWeek;
    return startOfWeek.add(const Duration(days: 6));
  }

  /// Get start of month
  static DateTime get startOfMonth {
    final now = DateTime.now();
    return DateTime(now.year, now.month, 1);
  }

  /// Get end of month
  static DateTime get endOfMonth {
    final now = DateTime.now();
    return DateTime(now.year, now.month + 1, 0);
  }

  /// Get start of year
  static DateTime get startOfYear {
    final now = DateTime.now();
    return DateTime(now.year, 1, 1);
  }

  /// Get end of year
  static DateTime get endOfYear {
    final now = DateTime.now();
    return DateTime(now.year, 12, 31);
  }

  /// Check if date is today
  static bool isToday(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year && date.month == now.month && date.day == now.day;
  }

  /// Check if date is yesterday
  static bool isYesterday(DateTime date) {
    final yesterday = DateTime.now().subtract(const Duration(days: 1));
    return date.year == yesterday.year && date.month == yesterday.month && date.day == yesterday.day;
  }

  /// Check if date is tomorrow
  static bool isTomorrow(DateTime date) {
    final tomorrow = DateTime.now().add(const Duration(days: 1));
    return date.year == tomorrow.year && date.month == tomorrow.month && date.day == tomorrow.day;
  }

  /// Check if date is this week
  static bool isThisWeek(DateTime date) {
    final startOfWeek = AppDateUtils.startOfWeek;
    final endOfWeek = AppDateUtils.endOfWeek;
    return date.isAfter(startOfWeek.subtract(const Duration(days: 1))) && 
           date.isBefore(endOfWeek.add(const Duration(days: 1)));
  }

  /// Check if date is this month
  static bool isThisMonth(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year && date.month == now.month;
  }

  /// Check if date is this year
  static bool isThisYear(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year;
  }

  /// Get difference in days
  static int daysBetween(DateTime from, DateTime to) {
    from = DateTime(from.year, from.month, from.day);
    to = DateTime(to.year, to.month, to.day);
    return to.difference(from).inDays;
  }

  /// Get difference in hours
  static int hoursBetween(DateTime from, DateTime to) {
    return to.difference(from).inHours;
  }

  /// Get difference in minutes
  static int minutesBetween(DateTime from, DateTime to) {
    return to.difference(from).inMinutes;
  }

  /// Get age from birth date
  static int getAge(DateTime birthDate) {
    final now = DateTime.now();
    int age = now.year - birthDate.year;
    if (now.month < birthDate.month || (now.month == birthDate.month && now.day < birthDate.day)) {
      age--;
    }
    return age;
  }

  /// Get relative time string (e.g., "2 hours ago", "in 3 days")
  static String getRelativeTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = dateTime.difference(now);
    final absDifference = difference.abs();

    if (absDifference.inDays > 365) {
      final years = (absDifference.inDays / 365).floor();
      return difference.isNegative 
          ? '$years year${years == 1 ? '' : 's'} ago'
          : 'in $years year${years == 1 ? '' : 's'}';
    } else if (absDifference.inDays > 30) {
      final months = (absDifference.inDays / 30).floor();
      return difference.isNegative 
          ? '$months month${months == 1 ? '' : 's'} ago'
          : 'in $months month${months == 1 ? '' : 's'}';
    } else if (absDifference.inDays > 0) {
      return difference.isNegative 
          ? '${absDifference.inDays} day${absDifference.inDays == 1 ? '' : 's'} ago'
          : 'in ${absDifference.inDays} day${absDifference.inDays == 1 ? '' : 's'}';
    } else if (absDifference.inHours > 0) {
      return difference.isNegative 
          ? '${absDifference.inHours} hour${absDifference.inHours == 1 ? '' : 's'} ago'
          : 'in ${absDifference.inHours} hour${absDifference.inHours == 1 ? '' : 's'}';
    } else if (absDifference.inMinutes > 0) {
      return difference.isNegative 
          ? '${absDifference.inMinutes} minute${absDifference.inMinutes == 1 ? '' : 's'} ago'
          : 'in ${absDifference.inMinutes} minute${absDifference.inMinutes == 1 ? '' : 's'}';
    } else {
      return 'Just now';
    }
  }

  /// Get friendly date string (e.g., "Today", "Yesterday", "Tomorrow", or formatted date)
  static String getFriendlyDate(DateTime date) {
    if (isToday(date)) return 'Today';
    if (isYesterday(date)) return 'Yesterday';
    if (isTomorrow(date)) return 'Tomorrow';
    return formatDate(date);
  }

  /// Get friendly date and time string
  static String getFriendlyDateTime(DateTime dateTime) {
    final dateStr = getFriendlyDate(dateTime);
    final timeStr = formatTime(dateTime);
    return '$dateStr at $timeStr';
  }

  /// Get month name
  static String getMonthName(int month) {
    const months = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    return months[month - 1];
  }

  /// Get short month name
  static String getShortMonthName(int month) {
    const months = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];
    return months[month - 1];
  }

  /// Get day name
  static String getDayName(int weekday) {
    const days = [
      'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'
    ];
    return days[weekday - 1];
  }

  /// Get short day name
  static String getShortDayName(int weekday) {
    const days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    return days[weekday - 1];
  }

  /// Add business days (excluding weekends)
  static DateTime addBusinessDays(DateTime date, int days) {
    DateTime result = date;
    int addedDays = 0;
    
    while (addedDays < days) {
      result = result.add(const Duration(days: 1));
      if (result.weekday < 6) { // Monday = 1, Friday = 5
        addedDays++;
      }
    }
    
    return result;
  }

  /// Check if date is weekend
  static bool isWeekend(DateTime date) {
    return date.weekday == 6 || date.weekday == 7; // Saturday or Sunday
  }

  /// Check if date is business day
  static bool isBusinessDay(DateTime date) {
    return !isWeekend(date);
  }

  /// Get quarter of year
  static int getQuarter(DateTime date) {
    return ((date.month - 1) / 3).floor() + 1;
  }

  /// Get start of quarter
  static DateTime getStartOfQuarter(DateTime date) {
    final quarter = getQuarter(date);
    final startMonth = (quarter - 1) * 3 + 1;
    return DateTime(date.year, startMonth, 1);
  }

  /// Get end of quarter
  static DateTime getEndOfQuarter(DateTime date) {
    final quarter = getQuarter(date);
    final endMonth = quarter * 3;
    return DateTime(date.year, endMonth + 1, 0);
  }
}
