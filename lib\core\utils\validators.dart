import '../constants/app_constants.dart';

/// Form validation utilities for Al Ameen Sales App
class Validators {
  // Prevent instantiation
  Validators._();

  /// Validate required field
  static String? required(String? value, {String? fieldName}) {
    if (value == null || value.trim().isEmpty) {
      return '${fieldName ?? 'This field'} is required';
    }
    return null;
  }

  /// Validate email
  static String? email(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Email is required';
    }
    
    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    if (!emailRegex.hasMatch(value.trim())) {
      return 'Please enter a valid email address';
    }
    
    return null;
  }

  /// Validate password
  static String? password(String? value) {
    if (value == null || value.isEmpty) {
      return 'Password is required';
    }
    
    if (value.length < AppConstants.minPasswordLength) {
      return 'Password must be at least ${AppConstants.minPasswordLength} characters';
    }
    
    if (value.length > AppConstants.maxPasswordLength) {
      return 'Password must be less than ${AppConstants.maxPasswordLength} characters';
    }
    
    return null;
  }

  /// Validate password confirmation
  static String? confirmPassword(String? value, String? password) {
    if (value == null || value.isEmpty) {
      return 'Please confirm your password';
    }
    
    if (value != password) {
      return 'Passwords do not match';
    }
    
    return null;
  }

  /// Validate phone number
  static String? phone(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Phone number is required';
    }
    
    final phoneRegex = RegExp(r'^\+?[\d\s\-\(\)]{10,}$');
    if (!phoneRegex.hasMatch(value.trim())) {
      return 'Please enter a valid phone number';
    }
    
    return null;
  }

  /// Validate name
  static String? name(String? value, {String? fieldName}) {
    if (value == null || value.trim().isEmpty) {
      return '${fieldName ?? 'Name'} is required';
    }
    
    if (value.trim().length < AppConstants.minNameLength) {
      return '${fieldName ?? 'Name'} must be at least ${AppConstants.minNameLength} characters';
    }
    
    if (value.trim().length > AppConstants.maxNameLength) {
      return '${fieldName ?? 'Name'} must be less than ${AppConstants.maxNameLength} characters';
    }
    
    return null;
  }

  /// Validate numeric value
  static String? numeric(String? value, {String? fieldName}) {
    if (value == null || value.trim().isEmpty) {
      return '${fieldName ?? 'This field'} is required';
    }
    
    if (double.tryParse(value.trim()) == null) {
      return 'Please enter a valid number';
    }
    
    return null;
  }

  /// Validate positive number
  static String? positiveNumber(String? value, {String? fieldName}) {
    final numericError = numeric(value, fieldName: fieldName);
    if (numericError != null) return numericError;
    
    final number = double.parse(value!.trim());
    if (number <= 0) {
      return '${fieldName ?? 'Value'} must be greater than 0';
    }
    
    return null;
  }

  /// Validate integer
  static String? integer(String? value, {String? fieldName}) {
    if (value == null || value.trim().isEmpty) {
      return '${fieldName ?? 'This field'} is required';
    }
    
    if (int.tryParse(value.trim()) == null) {
      return 'Please enter a valid whole number';
    }
    
    return null;
  }

  /// Validate positive integer
  static String? positiveInteger(String? value, {String? fieldName}) {
    final integerError = integer(value, fieldName: fieldName);
    if (integerError != null) return integerError;
    
    final number = int.parse(value!.trim());
    if (number <= 0) {
      return '${fieldName ?? 'Value'} must be greater than 0';
    }
    
    return null;
  }

  /// Validate minimum length
  static String? minLength(String? value, int minLength, {String? fieldName}) {
    if (value == null || value.isEmpty) {
      return '${fieldName ?? 'This field'} is required';
    }
    
    if (value.length < minLength) {
      return '${fieldName ?? 'This field'} must be at least $minLength characters';
    }
    
    return null;
  }

  /// Validate maximum length
  static String? maxLength(String? value, int maxLength, {String? fieldName}) {
    if (value != null && value.length > maxLength) {
      return '${fieldName ?? 'This field'} must be less than $maxLength characters';
    }
    
    return null;
  }

  /// Validate range
  static String? range(String? value, double min, double max, {String? fieldName}) {
    final numericError = numeric(value, fieldName: fieldName);
    if (numericError != null) return numericError;
    
    final number = double.parse(value!.trim());
    if (number < min || number > max) {
      return '${fieldName ?? 'Value'} must be between $min and $max';
    }
    
    return null;
  }

  /// Validate URL
  static String? url(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'URL is required';
    }
    
    final urlRegex = RegExp(r'^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$');
    if (!urlRegex.hasMatch(value.trim())) {
      return 'Please enter a valid URL';
    }
    
    return null;
  }

  /// Validate date
  static String? date(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Date is required';
    }
    
    try {
      DateTime.parse(value.trim());
      return null;
    } catch (e) {
      return 'Please enter a valid date';
    }
  }

  /// Validate future date
  static String? futureDate(String? value) {
    final dateError = date(value);
    if (dateError != null) return dateError;
    
    final selectedDate = DateTime.parse(value!.trim());
    final now = DateTime.now();
    
    if (selectedDate.isBefore(DateTime(now.year, now.month, now.day))) {
      return 'Date must be in the future';
    }
    
    return null;
  }

  /// Validate past date
  static String? pastDate(String? value) {
    final dateError = date(value);
    if (dateError != null) return dateError;
    
    final selectedDate = DateTime.parse(value!.trim());
    final now = DateTime.now();
    
    if (selectedDate.isAfter(DateTime(now.year, now.month, now.day))) {
      return 'Date must be in the past';
    }
    
    return null;
  }

  /// Validate description
  static String? description(String? value) {
    if (value != null && value.length > AppConstants.maxDescriptionLength) {
      return 'Description must be less than ${AppConstants.maxDescriptionLength} characters';
    }
    
    return null;
  }

  /// Validate dropdown selection
  static String? dropdown(dynamic value, {String? fieldName}) {
    if (value == null) {
      return 'Please select ${fieldName ?? 'an option'}';
    }
    
    return null;
  }

  /// Validate checkbox
  static String? checkbox(bool? value, {String? fieldName}) {
    if (value != true) {
      return 'Please check ${fieldName ?? 'this option'}';
    }
    
    return null;
  }

  /// Validate file size
  static String? fileSize(int? bytes) {
    if (bytes == null) {
      return 'File is required';
    }
    
    if (bytes > AppConstants.maxFileSize) {
      return 'File size must be less than ${AppConstants.maxFileSize / (1024 * 1024)}MB';
    }
    
    return null;
  }

  /// Validate file type
  static String? fileType(String? fileName, List<String> allowedTypes) {
    if (fileName == null || fileName.isEmpty) {
      return 'File is required';
    }
    
    final extension = fileName.split('.').last.toLowerCase();
    if (!allowedTypes.contains(extension)) {
      return 'File type must be one of: ${allowedTypes.join(', ')}';
    }
    
    return null;
  }

  /// Combine multiple validators
  static String? combine(String? value, List<String? Function(String?)> validators) {
    for (final validator in validators) {
      final error = validator(value);
      if (error != null) return error;
    }
    return null;
  }

  /// Custom validator function type
  typedef ValidatorFunction = String? Function(String?);

  /// Create a custom validator
  static ValidatorFunction custom(bool Function(String?) test, String errorMessage) {
    return (String? value) {
      if (!test(value)) {
        return errorMessage;
      }
      return null;
    };
  }
}
